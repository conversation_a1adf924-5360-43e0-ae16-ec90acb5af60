// 缓存管理模块
import type { Ref } from 'vue'
import { ref } from 'vue'
import { apiClient } from '../../api'
import { dbManager } from '../../db/db-manager'
import type { Message, ChatHistoryResponse, DatabaseQueryOptions } from './types'

export class CacheManager {
  private messages: Ref<Map<string, Message[]>>
  private isLoading: Ref<boolean>
  private error: Ref<string>
  private loadingUsers: Ref<Set<string>> = ref(new Set()) // 按用户ID管理加载状态（响应式）
  private paginationState: Ref<
    Map<
      string,
      {
        currentPage: number
        hasMore: boolean
        isLoading: boolean
      }
    >
  >

  constructor(
    messages: Ref<Map<string, Message[]>>,
    isLoading: Ref<boolean>,
    error: Ref<string>,
    paginationState: Ref<
      Map<
        string,
        {
          currentPage: number
          hasMore: boolean
          isLoading: boolean
        }
      >
    >
  ) {
    this.messages = messages
    this.isLoading = isLoading
    this.error = error
    this.paginationState = paginationState
  }

  // 初始化数据库
  async initDatabase(getCurrentUserId: () => string) {
    try {
      const currentUser = getCurrentUserId()
      if (!currentUser) {
        throw new Error('用户未登录，无法初始化数据库')
      }

      await dbManager.initialize(currentUser)
    } catch (err) {
      this.error.value = err instanceof Error ? err.message : '数据库初始化失败'
      throw err
    }
  }

  // 获取聊天历史
  async loadChatHistory(
    otherUserId: string,
    page = 1,
    limit = 50,
    updateChatSession: (userId: string, message: Message) => Promise<void>,
    createEmptySession: (userId: string) => Promise<any>
  ): Promise<boolean> {
    // 检查该用户是否正在加载中
    if (this.loadingUsers.value.has(otherUserId)) {
      return true // 跳过重复请求应该返回 true，表示"成功处理"
    }

    try {
      // 标记该用户为加载中
      console.log(`🔍 [CacheManager] 开始加载用户 ${otherUserId} 的聊天历史`)
      this.loadingUsers.value.add(otherUserId)
      console.log(`🔍 [CacheManager] 当前加载用户列表:`, Array.from(this.loadingUsers.value))

      // 更新全局加载状态（用于UI显示）
      this.isLoading.value = true
      this.error.value = ''
      let loadedMessages: Message[] = []
      let fromCache = false

      // 首先检查本地数据库是否有历史消息
      try {
        const hasLocalMessages = await dbManager.hasMessages(otherUserId)
        if (hasLocalMessages) {
          // 从本地数据库加载所有消息
          try {
            // 获取数据库中的总消息数，然后加载所有消息
            const totalMessages = await dbManager.getMessageCount(otherUserId)
            console.log(`🔍 [loadChatHistory] 数据库中有 ${totalMessages} 条消息，加载所有消息`)

            loadedMessages = await this.getMessagesFromDatabase(otherUserId, {
              limit: totalMessages, // 加载所有消息
              offset: 0
            })
            fromCache = true
          } catch (dbError) {
            console.error(`🔍 [loadChatHistory] 从数据库加载消息失败:`, dbError)
            // 数据库读取失败，降级到API加载
          }
        }
      } catch (dbCheckError) {
        console.error(`🔍 [loadChatHistory] 检查数据库失败:`, dbCheckError)
        // 数据库检查失败，直接使用API
      }

      // 如果没有从缓存加载到消息，则从API获取
      if (!fromCache) {
        try {
          const response = await this.getMessagesFromAPI(otherUserId, page, limit)
          if (response && response.success) {
            loadedMessages = response.messages
            // 更新分页状态
            this.updatePaginationState(otherUserId, page, response.pagination.hasMore, false)
            // 批量存储到数据库（不阻塞主流程）
            if (loadedMessages.length > 0) {
              await this.storeMessagesToDatabase(loadedMessages, otherUserId)
            }
          } else {
            console.error(`获取聊天历史失败 - 用户${otherUserId}, API响应:`, response)
            this.error.value = response?.message || '获取聊天历史失败，请检查网络连接'
            return false
          }
        } catch (apiError) {
          console.error(`🔍 [loadChatHistory] API请求失败:`, apiError)
          this.error.value =
            apiError instanceof Error ? apiError.message : '网络请求失败，请检查网络连接'
          return false
        }
      } else {
        // 从缓存加载时，设置分页状态
        try {
          const totalMessages = loadedMessages.length
          // 计算已加载的页数
          const loadedPages = Math.ceil(totalMessages / limit)
          // 检查是否可能还有更多消息（如果消息数是完整页的倍数）
          const lastPageIsComplete = totalMessages % limit === 0
          const hasMore = lastPageIsComplete && totalMessages > 0

          console.log(
            `🔍 [loadChatHistory] 从数据库加载 ${totalMessages} 条消息, 相当于 ${loadedPages} 页, 最后一页完整: ${lastPageIsComplete}, 还有更多: ${hasMore}`
          )

          this.updatePaginationState(otherUserId, loadedPages, hasMore, false)
        } catch (error) {
          console.error(`🔍 [loadChatHistory] 设置分页状态失败，使用默认值:`, error)
          this.updatePaginationState(otherUserId, 1, true, false)
        }
      }

      // 将消息添加到store
      const newMessagesAdded = await this.mergeMessagesToStore(otherUserId, loadedMessages)

      // 更新聊天会话
      if (newMessagesAdded > 0) {
        const allMessages = this.messages.value.get(otherUserId) || []
        const lastMessage = allMessages[allMessages.length - 1]
        if (lastMessage) {
          await updateChatSession(otherUserId, lastMessage)
        }
      } else {
        // 即使没有消息，也要确保创建聊天会话（避免显示"暂无消息"）
        await createEmptySession(otherUserId)
      }

      return true
    } catch (err) {
      console.error(`🔍 [loadChatHistory] 获取用户${otherUserId}的聊天历史失败:`, err)
      this.error.value = err instanceof Error ? err.message : '获取聊天历史失败'
      return false
    } finally {
      // 移除该用户的加载状态
      console.log(`🔍 [CacheManager] 清除用户 ${otherUserId} 的加载状态`)
      this.loadingUsers.value.delete(otherUserId)
      console.log(`🔍 [CacheManager] 当前加载用户列表:`, Array.from(this.loadingUsers.value))
      // 如果没有其他用户在加载，则清除全局加载状态
      if (this.loadingUsers.value.size === 0) {
        this.isLoading.value = false
        console.log(`🔍 [CacheManager] 清除全局加载状态`)
      }
    }
  }

  // 加载更多历史消息
  async loadMoreHistory(
    otherUserId: string,
    updateChatSession: (userId: string, message: Message) => Promise<void>
  ): Promise<boolean> {
    // 获取当前分页状态
    const currentState = this.paginationState.value.get(otherUserId) || {
      currentPage: 1,
      hasMore: true,
      isLoading: false
    }

    // 检查是否还有更多消息或正在加载中
    if (!currentState.hasMore || currentState.isLoading) {
      return true
    }

    // 设置加载状态
    this.updatePaginationState(otherUserId, currentState.currentPage, currentState.hasMore, true)

    try {
      const nextPage = currentState.currentPage + 1
      const limit = 50
      let loadedMessages: Message[] = []
      let fromDatabase = false
      let hasMore = true

      console.log(
        `🔍 [loadMoreHistory] 开始加载更多历史消息 - 用户: ${otherUserId}, 页码: ${nextPage}`
      )

      // 首先检查数据库是否有该页的消息
      // 计算该页需要的最小消息数量：(nextPage - 1) * limit + 1
      const minRequiredMessages = (nextPage - 1) * limit + 1
      const hasEnoughMessages = await this.hasEnoughMessagesInDatabase(
        otherUserId,
        minRequiredMessages
      )

      if (hasEnoughMessages) {
        // 从数据库获取该页消息
        try {
          console.log(`🔍 [loadMoreHistory] 数据库有足够消息，从数据库获取第 ${nextPage} 页`)
          loadedMessages = await this.getMessagesFromDatabase(otherUserId, {
            limit,
            offset: (nextPage - 1) * limit
          })
          fromDatabase = true

          // 检查是否还有更多消息
          const totalMessages = await dbManager.getMessageCount(otherUserId)
          // 如果数据库中还有更多消息，或者数据库消息数是完整页的倍数（可能API还有更多）
          const hasMoreInDatabase = totalMessages > nextPage * limit
          const lastPageIsComplete = totalMessages % limit === 0
          hasMore = hasMoreInDatabase || (lastPageIsComplete && totalMessages > 0)

          console.log(
            `🔍 [loadMoreHistory] 从数据库获取 ${loadedMessages.length} 条消息，还有更多: ${hasMore}`
          )
        } catch (dbError) {
          console.error(`🔍 [loadMoreHistory] 从数据库获取消息失败，降级到API:`, dbError)
          fromDatabase = false
        }
      }

      // 如果数据库没有足够的消息或获取失败，则从API获取
      if (!fromDatabase) {
        console.log(`🔍 [loadMoreHistory] 从API获取第 ${nextPage} 页消息`)
        const response = await this.getMessagesFromAPI(otherUserId, nextPage, limit)
        if (response && response.success) {
          loadedMessages = response.messages
          hasMore = response.pagination.hasMore

          console.log(
            `🔍 [loadMoreHistory] 从API获取 ${loadedMessages.length} 条消息，还有更多: ${hasMore}`
          )

          // 将API获取的消息存储到数据库（不阻塞主流程）
          if (loadedMessages.length > 0) {
            this.storeMessagesToDatabase(loadedMessages, otherUserId).catch((error) => {
              console.error(`🔍 [loadMoreHistory] 存储API消息到数据库失败:`, error)
            })
          }
        } else {
          console.error(
            `🔍 [loadMoreHistory] API获取消息失败 - 用户${otherUserId}, 响应:`,
            response
          )
          this.error.value = response?.message || '加载更多消息失败'
          return false
        }
      }

      // 更新分页状态
      this.updatePaginationState(otherUserId, nextPage, hasMore, false)

      // 将新消息插入到现有消息列表的开头
      if (loadedMessages.length > 0) {
        await this.prependMessagesToStore(otherUserId, loadedMessages)

        // 更新聊天会话（使用最新的消息）
        const allMessages = this.messages.value.get(otherUserId) || []
        const lastMessage = allMessages[allMessages.length - 1]
        if (lastMessage) {
          await updateChatSession(otherUserId, lastMessage)
        }
      }

      console.log(
        `🔍 [loadMoreHistory] 成功加载 ${loadedMessages.length} 条消息 (来源: ${fromDatabase ? '数据库' : 'API'})`
      )
      return true
    } catch (error) {
      console.error(`🔍 [loadMoreHistory] 加载更多历史消息失败:`, error)
      this.error.value = error instanceof Error ? error.message : '加载更多消息失败'
      return false
    } finally {
      // 清除加载状态
      const currentState = this.paginationState.value.get(otherUserId)
      if (currentState) {
        this.updatePaginationState(
          otherUserId,
          currentState.currentPage,
          currentState.hasMore,
          false
        )
      }
    }
  }

  // 从数据库获取消息
  private async getMessagesFromDatabase(
    userId: string,
    options: DatabaseQueryOptions
  ): Promise<Message[]> {
    const { limit = 50, offset = 0 } = options
    console.log(
      `🔍 [getMessagesFromDatabase] 从数据库获取消息 - 用户: ${userId}, limit: ${limit}, offset: ${offset}`
    )

    try {
      const messages = await dbManager.getMessages(userId, options)
      console.log(`🔍 [getMessagesFromDatabase] 成功获取 ${messages.length} 条消息`)
      return messages
    } catch (error) {
      console.error(`🔍 [getMessagesFromDatabase] 从数据库获取消息失败:`, error)
      throw error
    }
  }

  // 从API获取消息
  private async getMessagesFromAPI(
    otherUserId: string,
    page: number,
    limit: number
  ): Promise<ChatHistoryResponse | null> {
    const response = await apiClient.getChatHistory(otherUserId, page, limit)
    if (response && response.success) {
      // 处理API返回的消息，转换时间戳格式
      const processedMessages = (response.messages || []).map((msg) => ({
        ...msg,
        timestamp:
          typeof msg.timestamp === 'string' ? new Date(msg.timestamp).getTime() : msg.timestamp
      }))

      return {
        ...response,
        messages: processedMessages
      }
    }

    return response
  }

  // 批量存储消息到数据库
  private async storeMessagesToDatabase(messages: Message[], userId: string) {
    try {
      await dbManager.storeMessages(messages, userId)
    } catch (storeError) {
      console.error(`🔍 [storeMessagesToDatabase] 批量存储消息到数据库失败:`, storeError)
      // 存储失败不影响消息显示
    }
  }

  // 将消息合并到store中
  private async mergeMessagesToStore(userId: string, newMessages: Message[]): Promise<number> {
    const existingMessages = this.messages.value.get(userId) || []
    const filteredNewMessages = newMessages.filter(
      (msg) => !existingMessages.some((existing) => existing.id === msg.id)
    )
    if (filteredNewMessages.length > 0) {
      const allMessages = [...filteredNewMessages, ...existingMessages].sort(
        (a, b) => a.timestamp - b.timestamp
      )
      this.messages.value.set(userId, allMessages)
    }

    return filteredNewMessages.length
  }

  // 存储单条消息到数据库
  async storeMessage(message: Message, userId: string) {
    try {
      await dbManager.storeMessage(message, userId)
    } catch (error) {
      console.error(`🔍 [storeMessage] 消息存储到数据库失败: ${message.id}`, error)
      throw error
    }
  }

  // 检查数据库是否有消息
  async hasMessages(userId: string): Promise<boolean> {
    try {
      return await dbManager.hasMessages(userId)
    } catch (error) {
      console.error(`🔍 [hasMessages] 检查数据库消息失败:`, error)
      return false
    }
  }

  // 检查数据库是否有足够的消息支持指定页码
  private async hasEnoughMessagesInDatabase(
    userId: string,
    minRequiredMessages: number
  ): Promise<boolean> {
    try {
      const messageCount = await dbManager.getMessageCount(userId)
      const hasEnough = messageCount >= minRequiredMessages
      console.log(
        `🔍 [hasEnoughMessagesInDatabase] 用户 ${userId} 数据库消息数: ${messageCount}, 最少需要: ${minRequiredMessages}, 足够: ${hasEnough}`
      )
      return hasEnough
    } catch (error) {
      console.error(`🔍 [hasEnoughMessagesInDatabase] 检查数据库消息数量失败:`, error)
      return false
    }
  }

  // 更新分页状态
  private updatePaginationState(
    userId: string,
    page: number,
    hasMore: boolean,
    isLoading: boolean
  ) {
    this.paginationState.value.set(userId, {
      currentPage: page,
      hasMore,
      isLoading
    })
  }

  // 将新消息插入到现有消息列表的开头
  private async prependMessagesToStore(userId: string, newMessages: Message[]): Promise<void> {
    const existingMessages = this.messages.value.get(userId) || []

    // 过滤掉重复的消息
    const filteredNewMessages = newMessages.filter(
      (msg) => !existingMessages.some((existing) => existing.id === msg.id)
    )

    if (filteredNewMessages.length > 0) {
      // 将新消息插入到开头，保持时间顺序
      const allMessages = [...filteredNewMessages, ...existingMessages].sort(
        (a, b) => a.timestamp - b.timestamp
      )
      this.messages.value.set(userId, allMessages)
    }
  }

  // 检查特定用户是否正在加载
  isUserLoading(userId: string): boolean {
    const isLoading = this.loadingUsers.value.has(userId)
    return isLoading
  }

  // 获取当前正在加载的用户列表
  getLoadingUsers(): string[] {
    return Array.from(this.loadingUsers.value)
  }

  // 强制停止特定用户的加载状态（用于异常情况）
  forceStopUserLoading(userId: string) {
    console.log(`🔍 [CacheManager] 强制停止用户 ${userId} 的加载状态`)
    if (this.loadingUsers.value.has(userId)) {
      this.loadingUsers.value.delete(userId)
      console.log(
        `🔍 [CacheManager] 强制清除后的加载用户列表:`,
        Array.from(this.loadingUsers.value)
      )
      // 如果没有其他用户在加载，则清除全局加载状态
      if (this.loadingUsers.value.size === 0) {
        this.isLoading.value = false
        console.log(`🔍 [CacheManager] 强制清除全局加载状态`)
      }
    }
  }

  // 清除缓存
  clearCache() {
    this.messages.value.clear()
    // 同时清除所有加载状态
    this.loadingUsers.value.clear()
    this.isLoading.value = false
  }

  // 清除特定用户的缓存
  clearUserCache(userId: string) {
    this.messages.value.delete(userId)
    // 同时清除该用户的加载状态
    this.forceStopUserLoading(userId)
  }
}
